 [UPDATE UI] Preservata posizione iniziale originale: a6
 [ADVANTAGE] <PERSON><PERSON><PERSON> vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: <PERSON><PERSON><PERSON> (0-0), <PERSON><PERSON> (0-0), <PERSON><PERSON><PERSON><PERSON> (0-0)
 [ADVANTAGE] <PERSON><PERSON> van<PERSON>gio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player1-hand: previousSize=null, currentSize=4, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1800
 [TIMER DEBUG] - player2TotalTime: 1800
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [EARLY PLAYER2 MONITOR] textContent cambiato: bruscolino Causa: childList Valore precedente: null
 [EARLY PLAYER2 MONITOR] Stack trace della modifica
(anonymous) @ script.js:12420
(anonymous) @ script.js:12418
 [SETUP ANIMATION] Animazione terminata - drag and drop sarà abilitato da animatePlayerNames()
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [UPDATE UI] Preservata posizione iniziale originale: a6
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #1 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=4, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1800
 [TIMER DEBUG] - player2TotalTime: 1800
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [PLAYER NAMES ANIMATION] Terminando animazione
 [PLAYER NAMES ANIMATION] Avvio fade-in immediato del game container
 [PLAYER NAMES ANIMATION] Fade-in avviato immediatamente
 [PSN] Usando handSize dal server per black: 5 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [PLAYER NAMES ANIMATION] Animazione completata e rimossa
 [PLAYER NAMES ANIMATION] Fade-in completato
 [PLAYER NAMES ANIMATION] isSetupAnimating resettato a false alla fine reale dell'animazione
 [PLAYER NAMES ANIMATION] nameAnimationCompleted impostato a true - animazione nomi completata
 [PLAYER NAMES ANIMATION] Forzando re-rendering delle carte dopo reset isSetupAnimating...
 [SETUP RE-RENDER] Player player1 (white): isLocal=false, isMyTurn=true, clickable=false
 [SETUP RE-RENDER] originalCurrentPlayerId: MYwK511AfOIDcN8JAAAp, myPlayerId: MYwK511AfOIDcN8JAAAp
 [SETUP RE-RENDER] myColor: black, playerColor: white
 [SETUP RE-RENDER] Re-rendering mano white (player1), clickable: false
 [SETUP RE-RENDER] Carte esistenti nel DOM: 4
  [ERROR] Errore JavaScript rilevato: 
(anonymous) @ multiplayer.js:1837
  [ERROR] Messaggio: Uncaught ReferenceError: handleCardDragStart is not defined
(anonymous) @ multiplayer.js:1838
  [ERROR] File: http://localhost:3000/script.js
(anonymous) @ multiplayer.js:1839
  [ERROR] Linea: 847
(anonymous) @ multiplayer.js:1840
  [ERROR] Stack: ReferenceError: handleCardDragStart is not defined
    at http://localhost:3000/script.js:847:66
    at NodeList.forEach (<anonymous>)
    at http://localhost:3000/script.js:826:27
    at Array.forEach (<anonymous>)
    at forceReRenderCardsAfterSetup (http://localhost:3000/script.js:783:43)
    at http://localhost:3000/script.js:9391:21
(anonymous) @ multiplayer.js:1841
script.js:847  Uncaught 
 [DRAG START] Inizio trascinamento carta Rock-A del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Rock Card value: A Type of value: string
 Current player hand: Array(5)
 Full hand data: [{"id":"Rock-3","suit":"Rock","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Scissors-9","suit":"Scissors","value":"9"},{"id":"Paper-3","suit":"Paper","value":"3"},{"id":"Rock-A","suit":"Rock","value":"A"}]
 Card data being sent: Object
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-a4 Event target: cell-a4
 [DROP SUCCESS] Carta rilasciata su cella: cell-a4 Drop ID: 1751023259606_fqmdz96yz Timestamp: 2025-06-27T11:20:59.606Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751023259606_fqmdz96yz
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: black
 [DROP DEBUG] effectiveTurnPlayerId: player2
 [DROP DEBUG] turnPlayer extracted: Object
 (black) tenta di piazzare [DRAG] A di Rock su a4
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751023259606_fqmdz96yz
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: a4
 [ONLINE GAME] Posizionamento carta: Object in a4
 [DROP] Carta temporanea aggiunta alla cella: a4
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: MYwK511AfOIDcN8JAAAp per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: MYwK511AfOIDcN8JAAAp
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751023259606_fqmdz96yz
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 1, Nero - Rock A su a4. Prossimo giocatore: white. StateTurn: 1
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🛡️  Setup appena completato - attivo protezione post-setup per 3 secondi
 [TURN PROTECTION] wasInSetupPhase: true → isInSetupPhase: false
 [TURN PROTECTION] postSetupProtectionUntil impostato a: 2025-06-27T11:21:02.644Z
 [TURN PROTECTION] updatedIsInPostSetupProtection: true
 [TURN PROTECTION] updatedNeedsProtection: true
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] this.opponentId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] state.currentPlayerId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(4)
 [GAME STATE] player2 (black): Array(4)
 [PERMANENT NAMES] Nomi permanenti finali: {"MYwK511AfOIDcN8JAAAp":"bruscolino","P7NVqlyQ86SBUrvGAAAj":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"MYwK511AfOIDcN8JAAAp":"black","P7NVqlyQ86SBUrvGAAAj":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 4
 [PSN API] Aggiornato handSize nero: 4
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: QFP7KG
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: QFP7KG
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #2 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=4, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player2-hand: previousSize=undefined, currentSize=4, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1800
 [TIMER DEBUG] - player2TotalTime: 1796
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Usando handSize dal server per white: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SOCKET] Cambio di visibilità del client P7NVqlyQ86SBUrvGAAAj: visible  
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: MYwK511AfOIDcN8JAAAp per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: MYwK511AfOIDcN8JAAAp
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 2, Bianco - Paper 2 su b4. Prossimo giocatore: black. StateTurn: 2
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] this.opponentId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] state.currentPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(3)
 [GAME STATE] player2 (black): Array(4)
 [PERMANENT NAMES] Nomi permanenti finali: {"MYwK511AfOIDcN8JAAAp":"bruscolino","P7NVqlyQ86SBUrvGAAAj":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"MYwK511AfOIDcN8JAAAp":"black","P7NVqlyQ86SBUrvGAAAj":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 3
 [PSN API] Aggiornato handSize nero: 4
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: QFP7KG
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: QFP7KG
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #3 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=3, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Paper-2
 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-2 da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Paper-2 rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in b4
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in b4
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1797
 [TIMER DEBUG] - player2TotalTime: 1796
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [PSN] Usando handSize dal server per black: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [VISIBILITY] NON riavvio animazione distribuzione carte, mostro stato attuale
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client P7NVqlyQ86SBUrvGAAAj: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [VISIBILITY DEBUG] currentGameState presente: true
 [VISIBILITY DEBUG] renderGameState definito: true
 [VISIBILITY DEBUG] isMultiplayerGame: true
 [VISIBILITY DEBUG] currentGameState.mode: online
 [VISIBILITY] Partita multiplayer rilevata, skip renderGameState per evitare corruzione stato
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [DRAG START] Inizio trascinamento carta Scissors-9 del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Scissors Card value: 9 Type of value: string
 Current player hand: Array(4)
 Full hand data: [{"id":"Rock-3","suit":"Rock","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Scissors-9","suit":"Scissors","value":"9"},{"id":"Paper-3","suit":"Paper","value":"3"}]
 Card data being sent: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-c4 Event target: cell-c4
 [DROP SUCCESS] Carta rilasciata su cella: cell-c4 Drop ID: 1751023266343_q13uj062a Timestamp: 2025-06-27T11:21:06.343Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751023266343_q13uj062a
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: black
 [DROP DEBUG] effectiveTurnPlayerId: player2
 [DROP DEBUG] turnPlayer extracted: Object
 (black) tenta di piazzare [DRAG] 9 di Scissors su c4
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751023266343_q13uj062a
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: c4
 [ONLINE GAME] Posizionamento carta: Object in c4
 [DROP] Carta temporanea aggiunta alla cella: c4
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: MYwK511AfOIDcN8JAAAp per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: MYwK511AfOIDcN8JAAAp
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751023266343_q13uj062a
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 2, Nero - Scissors 9 su c4. Prossimo giocatore: white. StateTurn: 2
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] this.opponentId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] state.currentPlayerId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(3)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"MYwK511AfOIDcN8JAAAp":"bruscolino","P7NVqlyQ86SBUrvGAAAj":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"MYwK511AfOIDcN8JAAAp":"black","P7NVqlyQ86SBUrvGAAAj":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 3
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: QFP7KG
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: QFP7KG
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #4 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: P7NVqlyQ86SBUrvGAAAj
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player1-hand: previousSize=3, currentSize=3, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=3, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-9
 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-9 da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Scissors-9 rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: P7NVqlyQ86SBUrvGAAAj
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1797
 [TIMER DEBUG] - player2TotalTime: 1794
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Usando handSize dal server per white: 3 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SOCKET] Cambio di visibilità del client P7NVqlyQ86SBUrvGAAAj: visible  
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: MYwK511AfOIDcN8JAAAp per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: MYwK511AfOIDcN8JAAAp
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 3, Bianco - Scissors 3 su b3. Prossimo giocatore: black. StateTurn: 3
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] this.opponentId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] state.currentPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"MYwK511AfOIDcN8JAAAp":"bruscolino","P7NVqlyQ86SBUrvGAAAj":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"MYwK511AfOIDcN8JAAAp":"black","P7NVqlyQ86SBUrvGAAAj":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: QFP7KG
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: QFP7KG
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #5 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=3, currentSize=2, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-3
 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-3 da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Scissors-3 rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in b3
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in b3
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1794
 [TIMER DEBUG] - player2TotalTime: 1794
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [PSN] Controllo periodico: Ripristino notazione esistente
 [PSN] Usando handSize dal server per black: 3 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client P7NVqlyQ86SBUrvGAAAj: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [DRAG START] Inizio trascinamento carta Paper-3 del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Paper Card value: 3 Type of value: string
 Current player hand: Array(3)
 Full hand data: [{"id":"Rock-3","suit":"Rock","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-3","suit":"Paper","value":"3"}]
 Card data being sent: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [DRAG END] DropEffect: none
 [DRAG START] Inizio trascinamento carta Rock-3 del giocatore black
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: black
 [DEBUG] effectiveTurnPlayerId: player2
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Rock Card value: 3 Type of value: string
 Current player hand: Array(3)
 Full hand data: [{"id":"Rock-3","suit":"Rock","value":"3"},{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-3","suit":"Paper","value":"3"}]
 Card data being sent: Object
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-b2 Event target: cell-b2
 [DROP SUCCESS] Carta rilasciata su cella: cell-b2 Drop ID: 1751023273893_4co3yqg7v Timestamp: 2025-06-27T11:21:13.893Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751023273893_4co3yqg7v
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: black
 [DROP DEBUG] effectiveTurnPlayerId: player2
 [DROP DEBUG] turnPlayer extracted: Object
 (black) tenta di piazzare [DRAG] 3 di Rock su b2
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751023273893_4co3yqg7v
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: b2
 [ONLINE GAME] Posizionamento carta: Object in b2
 [DROP] Carta temporanea aggiunta alla cella: b2
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: MYwK511AfOIDcN8JAAAp per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: MYwK511AfOIDcN8JAAAp
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751023273893_4co3yqg7v
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 3, Nero - Rock 3 su b2. Prossimo giocatore: white. StateTurn: 3
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] this.opponentId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] state.currentPlayerId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"MYwK511AfOIDcN8JAAAp":"bruscolino","P7NVqlyQ86SBUrvGAAAj":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"MYwK511AfOIDcN8JAAAp":"black","P7NVqlyQ86SBUrvGAAAj":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: QFP7KG
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: QFP7KG
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #6 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: P7NVqlyQ86SBUrvGAAAj
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=2, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=2, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Rock-3
 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-3 da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Rock-3 rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: P7NVqlyQ86SBUrvGAAAj
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1794
 [TIMER DEBUG] - player2TotalTime: 1791
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [PSN] Usando handSize dal server per white: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [SOCKET] Cambio di visibilità del client P7NVqlyQ86SBUrvGAAAj: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: MYwK511AfOIDcN8JAAAp per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: MYwK511AfOIDcN8JAAAp
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 4, Bianco - Scissors 4 su b6. Prossimo giocatore: black. StateTurn: 4
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] this.opponentId: P7NVqlyQ86SBUrvGAAAj
 [ONLINE GAME MAPPING] state.currentPlayerId: MYwK511AfOIDcN8JAAAp
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(1)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"MYwK511AfOIDcN8JAAAp":"bruscolino","P7NVqlyQ86SBUrvGAAAj":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"MYwK511AfOIDcN8JAAAp":"black","P7NVqlyQ86SBUrvGAAAj":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 1
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: QFP7KG
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: QFP7KG
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (1-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 1
 [ADVANTAGE] Percentuale finale: 56.7%
 [DEBUG] Vantaggio calcolato: 56.67%
 [HISTORY] Aggiunta mossa #7 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: MYwK511AfOIDcN8JAAAp
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=1, hasPlayedCard=true
script.js:4808 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
script.js:4815 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-4
script.js:4863 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-4 da player1-hand
script.js:4873 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
script.js:4826 [OPPONENT HAND] Carta dell'avversario Scissors-4 rimossa permanentemente da player1-hand - NON verrà ricreata
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 2
script.js:14914 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14915 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:5308 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5321 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:11695 [OPPONENT MARKER] Identificata ultima mossa avversario in b6
script.js:11784 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in b6
script.js:14917 [DEBUG] updateGameUI AFTER renderBoard
script.js:14918 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14919 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14939 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14940 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:16253 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
script.js:13107 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1533 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1535 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
multiplayer.js:1536 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1565 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1566 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1567 [TIMER DEBUG] - isMyTurn: true
multiplayer.js:1568 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1571 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1573 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
multiplayer.js:1006 [TIMER] Avvio timer di turno: 60 secondi rimanenti
multiplayer.js:1588 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1589 [TIMER DEBUG] - window: true
multiplayer.js:1590 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1591 [TIMER DEBUG] - player1TotalTime: 1790
multiplayer.js:1592 [TIMER DEBUG] - player2TotalTime: 1791
multiplayer.js:1593 [TIMER DEBUG] - gameState.currentPlayerId: player2
multiplayer.js:1605 [TIMER] Modalità online - currentPlayerId: player2
multiplayer.js:1606 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
multiplayer.js:1677 [TIMER] Avviato timer totale per giocatore 2
psn-unified.js:1237 [PSN] Usando handSize dal server per black: 2 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
script.js:1549 [SOCKET] Cambio di visibilità del client P7NVqlyQ86SBUrvGAAAj: hidden  
script.js:2370 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2373 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
psn-unified.js:882 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
psn-unified.js:915 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
script.js:10048 [DRAG START] Inizio trascinamento carta Paper-3 del giocatore black
script.js:10049 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
script.js:10089 [DRAG START] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
script.js:10094 [DRAG ONLINE] È il mio turno? true
script.js:10100 [DRAG ONLINE] Il mio colore: black
script.js:10119 [DEBUG] effectiveTurnPlayerId: player2
script.js:10120 [DEBUG] turnPlayer extracted: Object
script.js:10183 Drag Start: Object
script.js:10184 Card suit: Paper Card value: 3 Type of value: string
script.js:10188 Current player hand: Array(2)
script.js:10194 Full hand data: [{"id":"Paper-8","suit":"Paper","value":"8"},{"id":"Paper-3","suit":"Paper","value":"3"}]
script.js:10223 Card data being sent: Object
script.js:5273 [CELL DROP LISTENER] Evento drop catturato su cella: cell-b1 Event target: cell-b1
script.js:10327 [DROP SUCCESS] Carta rilasciata su cella: cell-b1 Drop ID: 1751023282205_d5w9wj5xi Timestamp: 2025-06-27T11:21:22.205Z
script.js:10354 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751023282205_d5w9wj5xi
script.js:10363 Dropped Card Data: Object
script.js:10380 [DROP CELL] Protezione turno non attiva - Uso ID dal server: MYwK511AfOIDcN8JAAAp
script.js:10385 [DROP ONLINE] È il mio turno? true
script.js:10391 [DROP ONLINE] Il mio colore: black
script.js:10410 [DROP DEBUG] effectiveTurnPlayerId: player2
script.js:10411 [DROP DEBUG] turnPlayer extracted: Object
script.js:10562 (black) tenta di piazzare [DRAG] 3 di Paper su b1
script.js:10580 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751023282205_d5w9wj5xi
game-mode-manager.js:129 [GAME MODE] Tentativo di piazzare carta: Object in posizione: b1
online-game.js:232 [ONLINE GAME] Posizionamento carta: Object in b1
script.js:10640 [DROP] Carta temporanea aggiunta alla cella: b1
script.js:946 [OPPONENT MARKER] Rimuovendo marker esistente
script.js:10646 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
script.js:10275 [DRAG END] DropEffect: move
psn-unified.js:1237 [PSN] Usando handSize dal server per black: 2 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
script.js:11171 [ID SYNC] ID corretto confermato: MYwK511AfOIDcN8JAAAp per username: bruscolino
script.js:11184 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11185 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11186 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11187 [HANDLE GAME STATE] - currentMode: online
script.js:11188 [HANDLE GAME STATE] - window.myPlayerId: MYwK511AfOIDcN8JAAAp
script.js:11246 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
script.js:11263 [GAME STATE] Stato ricevuto mentre processingDropId = 1751023282205_d5w9wj5xi
script.js:11267 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 4, Nero - Paper 3 su b1. Prossimo giocatore: white. StateTurn: 4
psn-unified.js:1841 [PSN] Skip rilevazione pescate - registrazione dal server in corso
script.js:11960 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
script.js:11961 [TURN PROTECTION] Turno era protetto: P7NVqlyQ86SBUrvGAAAj
online-game.js:148 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
online-game.js:149 [ONLINE GAME MAPPING] this.myPlayerId: MYwK511AfOIDcN8JAAAp
online-game.js:150 [ONLINE GAME MAPPING] this.opponentId: P7NVqlyQ86SBUrvGAAAj
online-game.js:151 [ONLINE GAME MAPPING] state.currentPlayerId: P7NVqlyQ86SBUrvGAAAj
online-game.js:152 [ONLINE GAME MAPPING] localPlayerData: Object
online-game.js:153 [ONLINE GAME MAPPING] opponentPlayerData: Object
script.js:12158 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12161 [GAME STATE] player1 (white): Array(1)
script.js:12161 [GAME STATE] player2 (black): Array(1)
script.js:12381 [PERMANENT NAMES] Nomi permanenti finali: {"MYwK511AfOIDcN8JAAAp":"bruscolino","P7NVqlyQ86SBUrvGAAAj":"giggio"}
script.js:12403 [PERMANENT COLORS] Colori permanenti finali: {"MYwK511AfOIDcN8JAAAp":"black","P7NVqlyQ86SBUrvGAAAj":"white"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 1
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 1
script.js:12475 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12476 [ANIMATION DEBUG] - isStarting: false
script.js:12477 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12478 [ANIMATION DEBUG] - state.gameId: QFP7KG
script.js:12479 [ANIMATION DEBUG] - state.gameOver: false
script.js:12480 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12481 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12482 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12483 [ANIMATION DEBUG] - state.mode: online
script.js:12484 [ANIMATION DEBUG] - window.animationsCompletedForGame: QFP7KG
script.js:12485 [ANIMATION DEBUG] - needsAnimations: false
script.js:14422 [UPDATE UI] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
script.js:13618 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
script.js:13684 [ADVANTAGE] Componenti: Vertici (0-0), Carte (1-1), Adiacenze (0-0)
script.js:13685 [ADVANTAGE] Valore vantaggio calcolato: 0
script.js:13741 [ADVANTAGE] Percentuale finale: 50.0%
script.js:14450 [DEBUG] Vantaggio calcolato: 50%
script.js:15892 [HISTORY] Aggiunta mossa #8 alla cronologia
script.js:15932 Tentativo di forzare rendering rating...
script.js:15959 Ratings dopo init: Object
script.js:15968 Aggiornamento avatar player1: player1
script.js:15974 Aggiornamento avatar player2: player2
script.js:16253 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: P7NVqlyQ86SBUrvGAAAj
script.js:14522 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
script.js:14525 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
script.js:14540 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: P7NVqlyQ86SBUrvGAAAj
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14659 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4804 [OPPONENT HAND] player1-hand: previousSize=1, currentSize=1, hasPlayedCard=false
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4804 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=1, hasPlayedCard=true
script.js:4808 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
script.js:4815 [OPPONENT HAND] Carta giocata dall'avversario: Paper-3
script.js:4863 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-3 da player2-hand
script.js:4873 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
script.js:4826 [OPPONENT HAND] Carta dell'avversario Paper-3 rimossa permanentemente da player2-hand - NON verrà ricreata
script.js:14914 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14915 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:5308 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5321 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:14917 [DEBUG] updateGameUI AFTER renderBoard
script.js:14918 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14919 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:14939 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14940 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:16253 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: P7NVqlyQ86SBUrvGAAAj
script.js:13107 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1533 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1535 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
multiplayer.js:1536 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1565 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1566 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1567 [TIMER DEBUG] - isMyTurn: false
multiplayer.js:1568 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1571 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1577 [TIMER DEBUG] Non è il mio turno, fermo timer
multiplayer.js:1588 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1589 [TIMER DEBUG] - window: true
multiplayer.js:1590 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1591 [TIMER DEBUG] - player1TotalTime: 1790
multiplayer.js:1592 [TIMER DEBUG] - player2TotalTime: 1788
multiplayer.js:1593 [TIMER DEBUG] - gameState.currentPlayerId: player1
multiplayer.js:1605 [TIMER] Modalità online - currentPlayerId: player1
multiplayer.js:1606 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
multiplayer.js:1619 [TIMER] Avviato timer totale per giocatore 1
script.js:10660 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
script.js:2203 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2224 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2242 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2572 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2370 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2373 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
[NEW] Explain Console errors by using Copilot in Edge: click
         
         to explain an error. 
        Learn more
        Don't show again
psn-unified.js:882 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
psn-unified.js:915 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
script.js:1166 [SOCKET] Ricevuto evento gameState: {gameId: 'QFP7KG', mode: 'online', players: {…}, board: {…}, discardPile: Array(0), …}
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
