 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: white
 [DEBUG] effectiveTurnPlayerId: player1
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Scissors Card value: 10 Type of value: string
 Current player hand: Array(5)
 Full hand data: [{"id":"Paper-10","suit":"Paper","value":"10"},{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Scissors-10","suit":"Scissors","value":"10"},{"id":"Scissors-5","suit":"Scissors","value":"5"},{"id":"Rock-9","suit":"Rock","value":"9"}]
 Card data being sent: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-e1 Event target: cell-e1
 [DROP SUCCESS] Carta rilasciata su cella: cell-e1 Drop ID: 1751022490900_yj40vwp7r Timestamp: 2025-06-27T11:08:10.900Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751022490900_yj40vwp7r
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: white
 [DROP DEBUG] effectiveTurnPlayerId: player1
 [DROP DEBUG] turnPlayer extracted: Object
 (white) tenta di piazzare [DRAG] 10 di Scissors su e1
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751022490900_yj40vwp7r
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: e1
 [ONLINE GAME] Posizionamento carta: Object in e1
 [DROP] Carta temporanea aggiunta alla cella: e1
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN] Usando handSize dal server per white: 5 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751022490900_yj40vwp7r
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Gioco iniziato - prima mossa registrata
 [PSN] Prima mossa rilevata, cambio a vista Notazioni.
 [PSN] Mossa registrata (con debug): Turno 1, Bianco - Scissors 10 su e1. Prossimo giocatore: black. StateTurn: 1
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🛡️  Setup appena completato - attivo protezione post-setup per 3 secondi
 [TURN PROTECTION] wasInSetupPhase: true → isInSetupPhase: false
 [TURN PROTECTION] postSetupProtectionUntil impostato a: 2025-06-27T11:08:13.941Z
 [TURN PROTECTION] updatedIsInPostSetupProtection: true
 [TURN PROTECTION] updatedNeedsProtection: true
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] state.currentPlayerId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(4)
 [GAME STATE] player2 (black): Array(5)
 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 4
 [PSN API] Aggiornato handSize nero: 5
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #2 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player1-hand: previousSize=undefined, currentSize=4, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 5
 [OPPONENT HAND] player2-hand: previousSize=5, currentSize=5, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1795
 [TIMER DEBUG] - player2TotalTime: 1800
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: visible  
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 1, Nero - Scissors Q su d1. Prossimo giocatore: white. StateTurn: 1
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] state.currentPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(4)
 [GAME STATE] player2 (black): Array(4)
 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 4
 [PSN API] Aggiornato handSize nero: 4
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #3 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player2-hand: previousSize=5, currentSize=4, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-Q
 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-Q da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Scissors-Q rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in d1
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in d1
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1795
 [TIMER DEBUG] - player2TotalTime: 1796
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Usando handSize dal server per white: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [DRAG START] Inizio trascinamento carta Rock-9 del giocatore white
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: white
 [DEBUG] effectiveTurnPlayerId: player1
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Rock Card value: 9 Type of value: string
 Current player hand: Array(4)
 Full hand data: [{"id":"Paper-10","suit":"Paper","value":"10"},{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Scissors-5","suit":"Scissors","value":"5"},{"id":"Rock-9","suit":"Rock","value":"9"}]
 Card data being sent: Object
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-d2 Event target: cell-d2
 [DROP SUCCESS] Carta rilasciata su cella: cell-d2 Drop ID: 1751022499919_3czp249uk Timestamp: 2025-06-27T11:08:19.919Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751022499919_3czp249uk
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: white
 [DROP DEBUG] effectiveTurnPlayerId: player1
 [DROP DEBUG] turnPlayer extracted: Object
 (white) tenta di piazzare [DRAG] 9 di Rock su d2
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751022499919_3czp249uk
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: d2
 [ONLINE GAME] Posizionamento carta: Object in d2
 [DROP] Carta temporanea aggiunta alla cella: d2
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [PSN] Usando handSize dal server per white: 4 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [DRAG END] DropEffect: move
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751022499919_3czp249uk
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 2, Bianco - Rock 9 su d2. Prossimo giocatore: black. StateTurn: 2
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] state.currentPlayerId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(3)
 [GAME STATE] player2 (black): Array(4)
 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 3
 [PSN API] Aggiornato handSize nero: 4
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #4 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: fUUTdjAFVtq_FL7LAAAg
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player1-hand: previousSize=4, currentSize=3, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Rock-9
 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-9 da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Rock-9 rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=4, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: fUUTdjAFVtq_FL7LAAAg
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1791
 [TIMER DEBUG] - player2TotalTime: 1796
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 2, Nero - Rock K su d3. Prossimo giocatore: white. StateTurn: 2
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] state.currentPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(3)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 3
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 0
 [ADVANTAGE] Percentuale finale: 50.0%
 [DEBUG] Vantaggio calcolato: 50%
 [HISTORY] Aggiunta mossa #5 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=3, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Rock-K
 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-K da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Rock-K rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in d3
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in d3
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1791
 [TIMER DEBUG] - player2TotalTime: 1792
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Usando handSize dal server per white: 3 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [DRAG START] Inizio trascinamento carta Paper-J del giocatore white
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: white
 [DEBUG] effectiveTurnPlayerId: player1
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Paper Card value: J Type of value: string
 Current player hand: Array(3)
 Full hand data: [{"id":"Paper-10","suit":"Paper","value":"10"},{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Scissors-5","suit":"Scissors","value":"5"}]
 Card data being sent: Object
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-d4 Event target: cell-d4
 [DROP SUCCESS] Carta rilasciata su cella: cell-d4 Drop ID: 1751022508873_nwlpts5aq Timestamp: 2025-06-27T11:08:28.873Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751022508873_nwlpts5aq
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: white
 [DROP DEBUG] effectiveTurnPlayerId: player1
 [DROP DEBUG] turnPlayer extracted: Object
 (white) tenta di piazzare [DRAG] J di Paper su d4
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751022508873_nwlpts5aq
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: d4
 [ONLINE GAME] Posizionamento carta: Object in d4
 [DROP] Carta temporanea aggiunta alla cella: d4
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN] Usando handSize dal server per white: 3 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [PSN_VERTEX_CONTROL] Ricevuto evento vertex control dal server: Object
 [PSN_VERTEX_CONTROL] Notifica PSN per controllo vertice: posizione=f6, isWhite=true
 [PSN API] notifyVertexControlChange chiamata per posizione f6, isWhite=true
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f6 e giocatore white. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f6 (bianco) per applicazione futura: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751022508873_nwlpts5aq
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 3, Bianco - Paper J su d4 (controllo vertice ottenuto). Prossimo giocatore: black. StateTurn: 3
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] state.currentPlayerId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":"white"}
 [ADVANTAGE] Componenti: Vertici (1-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 1
 [ADVANTAGE] Percentuale finale: 56.7%
 [DEBUG] Vantaggio calcolato: 56.67%
 [HISTORY] Aggiunta mossa #6 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: fUUTdjAFVtq_FL7LAAAg
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=3, currentSize=2, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Paper-J
 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-J da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Paper-J rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=3, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color white
 [VERTEX CHANGE] Vertex vertex-f6 changed controller from null to white
 [VERTEX PSN] Notifica del cambio di controllo del vertice f6 a white (isWhite=true)
 [PSN API] notifyVertexControlChange chiamata per posizione f6, isWhite=true
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f6 e giocatore white. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f6 (bianco) per applicazione futura: Object
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: fUUTdjAFVtq_FL7LAAAg
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1787
 [TIMER DEBUG] - player2TotalTime: 1792
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: visible  
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 3, Nero - Scissors 4 su d5. Prossimo giocatore: white. StateTurn: 3
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] state.currentPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":"white"}
 [ADVANTAGE] Componenti: Vertici (1-0), Carte (0-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 1
 [ADVANTAGE] Percentuale finale: 56.7%
 [DEBUG] Vantaggio calcolato: 56.67%
 [HISTORY] Aggiunta mossa #7 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=2, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-4
 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-4 da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Scissors-4 rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in d5
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in d5
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color white
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1787
 [TIMER DEBUG] - player2TotalTime: 1788
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Usando handSize dal server per white: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [DRAG START] Inizio trascinamento carta Scissors-5 del giocatore white
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: white
 [DEBUG] effectiveTurnPlayerId: player1
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Scissors Card value: 5 Type of value: string
 Current player hand: Array(2)
 Full hand data: [{"id":"Paper-10","suit":"Paper","value":"10"},{"id":"Scissors-5","suit":"Scissors","value":"5"}]
 Card data being sent: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-d6 Event target: cell-d6
 [DROP SUCCESS] Carta rilasciata su cella: cell-d6 Drop ID: 1751022517133_udiy6tyib Timestamp: 2025-06-27T11:08:37.133Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751022517133_udiy6tyib
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: white
 [DROP DEBUG] effectiveTurnPlayerId: player1
 [DROP DEBUG] turnPlayer extracted: Object
 (white) tenta di piazzare [DRAG] 5 di Scissors su d6
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751022517133_udiy6tyib
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: d6
 [ONLINE GAME] Posizionamento carta: Object in d6
 [DROP] Carta temporanea aggiunta alla cella: d6
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN] Usando handSize dal server per white: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751022517133_udiy6tyib
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 4, Bianco - Scissors 5 su d6 (controllo vertice ottenuto). Prossimo giocatore: black. StateTurn: 4
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] state.currentPlayerId: fUUTdjAFVtq_FL7LAAAg
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(1)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 1
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":"white"}
 [ADVANTAGE] Componenti: Vertici (1-0), Carte (1-0), Adiacenze (0-0)
 [ADVANTAGE] Valore vantaggio calcolato: 2
 [ADVANTAGE] Percentuale finale: 63.3%
 [DEBUG] Vantaggio calcolato: 63.34%
 [HISTORY] Aggiunta mossa #8 alla cronologia
script.js:15938 Tentativo di forzare rendering rating...
script.js:15965 Ratings dopo init: Object
script.js:15974 Aggiornamento avatar player1: player1
script.js:15980 Aggiornamento avatar player2: player2
script.js:16259 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: fUUTdjAFVtq_FL7LAAAg
script.js:14528 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14531 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14546 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: fUUTdjAFVtq_FL7LAAAg
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14665 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4813 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=1, hasPlayedCard=true
script.js:4817 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
script.js:4824 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-5
script.js:4872 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-5 da player1-hand
script.js:4882 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
script.js:4835 [OPPONENT HAND] Carta dell'avversario Scissors-5 rimossa permanentemente da player1-hand - NON verrà ricreata
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 2
script.js:4813 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=2, hasPlayedCard=false
script.js:14920 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14921 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:5317 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5330 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:14923 [DEBUG] updateGameUI AFTER renderBoard
script.js:14924 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14925 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:5574 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color white
script.js:14945 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14946 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:16259 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: fUUTdjAFVtq_FL7LAAAg
script.js:13113 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1533 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1535 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
multiplayer.js:1536 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1565 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1566 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1567 [TIMER DEBUG] - isMyTurn: false
multiplayer.js:1568 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1571 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1577 [TIMER DEBUG] Non è il mio turno, fermo timer
multiplayer.js:1588 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1589 [TIMER DEBUG] - window: true
multiplayer.js:1590 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1591 [TIMER DEBUG] - player1TotalTime: 1784
multiplayer.js:1592 [TIMER DEBUG] - player2TotalTime: 1788
multiplayer.js:1593 [TIMER DEBUG] - gameState.currentPlayerId: player2
multiplayer.js:1605 [TIMER] Modalità online - currentPlayerId: player2
multiplayer.js:1606 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
multiplayer.js:1677 [TIMER] Avviato timer totale per giocatore 2
psn-unified.js:1156 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
script.js:2203 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2224 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2242 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2572 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:1549 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: visible  
script.js:10669 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
script.js:11180 [ID SYNC] ID corretto confermato: R11meEwi_vo8LrHWAAAi per username: bruscolino
script.js:11193 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11194 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11195 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11196 [HANDLE GAME STATE] - currentMode: online
script.js:11197 [HANDLE GAME STATE] - window.myPlayerId: R11meEwi_vo8LrHWAAAi
script.js:11255 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
psn-unified.js:1569 [PSN] Mossa registrata (con debug): Turno 4, Nero - Rock A su e6. Prossimo giocatore: white. StateTurn: 4
script.js:11966 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
script.js:11967 [TURN PROTECTION] Turno era protetto: R11meEwi_vo8LrHWAAAi
online-game.js:148 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
online-game.js:149 [ONLINE GAME MAPPING] this.myPlayerId: R11meEwi_vo8LrHWAAAi
online-game.js:150 [ONLINE GAME MAPPING] this.opponentId: fUUTdjAFVtq_FL7LAAAg
online-game.js:151 [ONLINE GAME MAPPING] state.currentPlayerId: R11meEwi_vo8LrHWAAAi
online-game.js:152 [ONLINE GAME MAPPING] localPlayerData: Object
online-game.js:153 [ONLINE GAME MAPPING] opponentPlayerData: Object
script.js:12164 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12167 [GAME STATE] player1 (white): Array(1)
script.js:12167 [GAME STATE] player2 (black): Array(1)
script.js:12387 [PERMANENT NAMES] Nomi permanenti finali: {"R11meEwi_vo8LrHWAAAi":"bruscolino","fUUTdjAFVtq_FL7LAAAg":"giggio"}
script.js:12409 [PERMANENT COLORS] Colori permanenti finali: {"R11meEwi_vo8LrHWAAAi":"white","fUUTdjAFVtq_FL7LAAAg":"black"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 1
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 1
script.js:12481 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12482 [ANIMATION DEBUG] - isStarting: false
script.js:12483 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12484 [ANIMATION DEBUG] - state.gameId: 2RJ1IV
script.js:12485 [ANIMATION DEBUG] - state.gameOver: false
script.js:12486 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12487 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12488 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12489 [ANIMATION DEBUG] - state.mode: online
script.js:12490 [ANIMATION DEBUG] - window.animationsCompletedForGame: 2RJ1IV
script.js:12491 [ANIMATION DEBUG] - needsAnimations: false
script.js:14428 [UPDATE UI] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
script.js:13624 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":null,"vertex-f1":null,"vertex-a6":null,"vertex-f6":"white"}
script.js:13690 [ADVANTAGE] Componenti: Vertici (1-0), Carte (1-1), Adiacenze (1-0)
script.js:13691 [ADVANTAGE] Valore vantaggio calcolato: 2
script.js:13747 [ADVANTAGE] Percentuale finale: 63.3%
script.js:14456 [DEBUG] Vantaggio calcolato: 63.34%
script.js:15898 [HISTORY] Aggiunta mossa #9 alla cronologia
script.js:15938 Tentativo di forzare rendering rating...
script.js:15965 Ratings dopo init: Object
script.js:15974 Aggiornamento avatar player1: player1
script.js:15980 Aggiornamento avatar player2: player2
script.js:16259 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
script.js:14528 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=bruscolino, P2 (NERO)=giggio
script.js:14531 [UI] Nomi predefiniti: P1=bruscolino, P2=giggio
script.js:14546 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: R11meEwi_vo8LrHWAAAi
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14665 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:5001 [CARD DRAG] Rendendo trascinabile ULTIMA carta Paper-10 in player1-hand
script.js:5006 [CARD DRAG] Listener aggiunti per ULTIMA carta. Draggable: true, Cursor: grab
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4813 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=1, hasPlayedCard=true
script.js:4817 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
script.js:4824 [OPPONENT HAND] Carta giocata dall'avversario: Rock-A
script.js:4872 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-A da player2-hand
script.js:4882 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
script.js:4835 [OPPONENT HAND] Carta dell'avversario Rock-A rimossa permanentemente da player2-hand - NON verrà ricreata
script.js:14920 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1274
script.js:14921 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1237
script.js:5317 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5330 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:11704 [OPPONENT MARKER] Identificata ultima mossa avversario in e6
script.js:11793 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in e6
script.js:14923 [DEBUG] updateGameUI AFTER renderBoard
script.js:14924 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1237
script.js:14925 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1274
script.js:5574 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color white
script.js:14945 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14946 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:16259 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
script.js:13113 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1533 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1535 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
multiplayer.js:1536 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1565 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1566 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1567 [TIMER DEBUG] - isMyTurn: true
multiplayer.js:1568 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1571 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1573 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
multiplayer.js:1006 [TIMER] Avvio timer di turno: 60 secondi rimanenti
multiplayer.js:1588 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1589 [TIMER DEBUG] - window: true
multiplayer.js:1590 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1591 [TIMER DEBUG] - player1TotalTime: 1784
multiplayer.js:1592 [TIMER DEBUG] - player2TotalTime: 1785
multiplayer.js:1593 [TIMER DEBUG] - gameState.currentPlayerId: player1
multiplayer.js:1605 [TIMER] Modalità online - currentPlayerId: player1
multiplayer.js:1606 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
multiplayer.js:1619 [TIMER] Avviato timer totale per giocatore 1
psn-unified.js:1237 [PSN] Usando handSize dal server per white: 1 carte rimanenti
psn-unified.js:1281 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
script.js:2370 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2373 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
script.js:1549 [SOCKET] Cambio di visibilità del client fUUTdjAFVtq_FL7LAAAg: hidden  
script.js:1555 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
psn-unified.js:882 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
psn-unified.js:915 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
script.js:10057 [DRAG START] Inizio trascinamento carta Paper-10 del giocatore white
script.js:10058 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
script.js:10098 [DRAG START] Protezione turno non attiva - Uso ID dal server: R11meEwi_vo8LrHWAAAi
script.js:10103 [DRAG ONLINE] È il mio turno? true
script.js:10109 [DRAG ONLINE] Il mio colore: white
script.js:10128 [DEBUG] effectiveTurnPlayerId: player1
script.js:10129 [DEBUG] turnPlayer extracted: Object
script.js:10192 Drag Start: Object
script.js:10193 Card suit: Paper Card value: 10 Type of value: string
script.js:10197 Current player hand: Array(1)
script.js:10203 Full hand data: [{"id":"Paper-10","suit":"Paper","value":"10"}]
script.js:10232 Card data being sent: Object
script.js:10284 [DRAG END] DropEffect: none
script.js:2203 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2224 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2242 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2572 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2370 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2373 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
psn-unified.js:882 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
psn-unified.js:915 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
script.js:1166 [SOCKET] Ricevuto evento gameState: {gameId: '2RJ1IV', mode: 'online', players: {…}, board: {…}, discardPile: Array(0), …}
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
