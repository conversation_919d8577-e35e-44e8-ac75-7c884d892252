 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [SOCKET] Errore di gioco ricevuto: Non puoi piazzare l'ultima carta sul vertice a1 perché non ne hai l'esclusiva.
 Errore di gioco: Non puoi piazzare l'ultima carta sul vertice a1 perché non ne hai l'esclusiva.
 [GAME ERROR] Reset isProcessingAction flag e processingDropId
 [PSN] Usando handSize dal server per white: 1 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [DROP PROTECTION] Pulizia carta temporanea scaduta in a1
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [PSN] Partita multiplayer - È il mio turno, permetto pesca carte
 [DRAW CARD] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [DRAW CARD] Modalità online - originalTurnPlayerId: BYf4pH-dYHLQg5E8AAA4 window.myPlayerId: BYf4pH-dYHLQg5E8AAA4 isMyTurn: true
 Tentativo di pescare una carta...
 [ONLINE GAME] === DEBUG DRAW CARD ===
 [ONLINE GAME] this.socket: true
 [ONLINE GAME] this.gameId: HNIEAW
 [ONLINE GAME] window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME] this.socket.id: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME] Pesca carta - invio gameAction al server...
 [ONLINE GAME] gameAction inviato per drawCard
 [DRAW CARD] Pulsante disabilitato, non è possibile pescare carte
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] state.currentPlayerId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: HNIEAW
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":null,"vertex-a6":"black","vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-2), Carte (0-0), Adiacenze (0-1)
 [ADVANTAGE] Valore vantaggio calcolato: -3
 [ADVANTAGE] Percentuale finale: 30.0%
 [DEBUG] Vantaggio calcolato: 29.990000000000002%
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [OPPONENT DRAW DETECTED] L'avversario ha pescato 1 carte
 [OPPONENT ANIMATION BLOCKED] Animazione carte avversario disabilitata per modalità multiplayer
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=1, currentSize=2, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=3, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1758
 [TIMER DEBUG] - player2TotalTime: 1768
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [PSN] Controllo periodico: Ripristino notazione esistente
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] state.currentPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(4)
 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 4
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: HNIEAW
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":null,"vertex-a6":"black","vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (0-2), Carte (0-0), Adiacenze (0-1)
 [ADVANTAGE] Valore vantaggio calcolato: -3
 [ADVANTAGE] Percentuale finale: 30.0%
 [DEBUG] Vantaggio calcolato: 29.990000000000002%
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [OPPONENT DRAW DETECTED] L'avversario ha pescato 1 carte
 [OPPONENT ANIMATION BLOCKED] Animazione carte avversario disabilitata per modalità multiplayer
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=4, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1758
 [TIMER DEBUG] - player2TotalTime: 1761
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Controllo periodico: Ripristino notazione esistente
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [DRAG START] Inizio trascinamento carta Rock-8 del giocatore white
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: white
 [DEBUG] effectiveTurnPlayerId: player1
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Rock Card value: 8 Type of value: string
 Current player hand: Array(2)
 Full hand data: [{"id":"Rock-8","suit":"Rock","value":"8"},{"id":"Paper-J","suit":"Paper","value":"J"}]
 Card data being sent: Object
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-d3 Event target: cell-d3
 [DROP SUCCESS] Carta rilasciata su cella: cell-d3 Drop ID: 1751023719324_elhw5q8bt Timestamp: 2025-06-27T11:28:39.324Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751023719324_elhw5q8bt
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: white
 [DROP DEBUG] effectiveTurnPlayerId: player1
 [DROP DEBUG] turnPlayer extracted: Object
 (white) tenta di piazzare [DRAG] 8 di Rock su d3
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751023719324_elhw5q8bt
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: d3
 [ONLINE GAME] Posizionamento carta: Object in d3
 [DROP] Carta temporanea aggiunta alla cella: d3
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [PSN] Usando handSize dal server per white: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [DRAG END] DropEffect: move
 [PSN_VERTEX_CONTROL] Ricevuto evento vertex control dal server: Object
 [PSN_VERTEX_CONTROL] Notifica PSN per controllo vertice: posizione=f1, isWhite=true
 [PSN API] notifyVertexControlChange chiamata per posizione f1, isWhite=true
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f1 e giocatore white. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f1 (bianco) per applicazione futura: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751023719324_elhw5q8bt
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 6, Bianco - Rock 8 su d3 (controllo vertice ottenuto). Prossimo giocatore: black. StateTurn: 6
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] state.currentPlayerId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(1)
 [GAME STATE] player2 (black): Array(4)
 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 1
 [PSN API] Aggiornato handSize nero: 4
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: HNIEAW
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":"white","vertex-a6":"black","vertex-f6":null}
 [ADVANTAGE] Componenti: Vertici (1-2), Carte (1-0), Adiacenze (0-1)
 [ADVANTAGE] Valore vantaggio calcolato: -1
 [ADVANTAGE] Percentuale finale: 43.3%
 [DEBUG] Vantaggio calcolato: 43.33%
 [HISTORY] Aggiunta mossa #9 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=1, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Rock-8
 [REMOVE OPPONENT CARD] Rimuovendo carta Rock-8 da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Rock-8 rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 4
 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=4, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color white
 [VERTEX CHANGE] Vertex vertex-f1 changed controller from null to white
 [VERTEX PSN] Notifica del cambio di controllo del vertice f1 a white (isWhite=true)
 [PSN API] notifyVertexControlChange chiamata per posizione f1, isWhite=true
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f1 e giocatore white. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f1 (bianco) per applicazione futura: Object
 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1751
 [TIMER DEBUG] - player2TotalTime: 1761
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [PSN_VERTEX_CONTROL] Ricevuto evento vertex control dal server: Object
 [PSN_VERTEX_CONTROL] Notifica PSN per controllo vertice: posizione=f6, isWhite=false
 [PSN API] notifyVertexControlChange chiamata per posizione f6, isWhite=false
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f6 e giocatore black. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f6 (nero) per applicazione futura: Object
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 6, Nero - Paper 5 su d4 (controllo vertice ottenuto). Prossimo giocatore: white. StateTurn: 6
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] state.currentPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(1)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 1
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: HNIEAW
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":"white","vertex-a6":"black","vertex-f6":"black"}
 [ADVANTAGE] Componenti: Vertici (1-3), Carte (1-0), Adiacenze (0-1)
 [ADVANTAGE] Valore vantaggio calcolato: -2
 [ADVANTAGE] Bianco ha possibilità di ribaltone (3-1)
 [ADVANTAGE] Bonus ribaltone applicato per il bianco
 [ADVANTAGE] Percentuale finale: 51.7%
 [DEBUG] Vantaggio calcolato: 51.66%
 [ADVANTAGE UI] Bianco ha possibilità di ribaltone (3-1)
 [HISTORY] Aggiunta mossa #10 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [CARD DRAG] Rendendo trascinabile ULTIMA carta Paper-J in player1-hand
 [CARD DRAG] Listener aggiunti per ULTIMA carta. Draggable: true, Cursor: grab
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player2-hand: previousSize=4, currentSize=3, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Paper-5
 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-5 da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Paper-5 rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in d4
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in d4
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color white
 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color black
 [VERTEX CHANGE] Vertex vertex-f6 changed controller from null to black
 [VERTEX PSN] Notifica del cambio di controllo del vertice f6 a black (isWhite=false)
 [PSN API] notifyVertexControlChange chiamata per posizione f6, isWhite=false
 [PSN API] Nessuna mossa ESATTA trovata in history per vertice f6 e giocatore black. Metto in pending.
 [PSN API] Memorizzata informazione di controllo vertice f6 (nero) per applicazione futura: Object
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1751
 [TIMER DEBUG] - player2TotalTime: 1757
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Controllo periodico: Ripristino notazione esistente
 [PSN] Usando handSize dal server per white: 1 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [PSN] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [PSN] Partita multiplayer - È il mio turno, permetto pesca carte
 [DRAW CARD] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [DRAW CARD] Modalità online - originalTurnPlayerId: BYf4pH-dYHLQg5E8AAA4 window.myPlayerId: BYf4pH-dYHLQg5E8AAA4 isMyTurn: true
 Tentativo di pescare una carta...
 [ONLINE GAME] === DEBUG DRAW CARD ===
 [ONLINE GAME] this.socket: true
 [ONLINE GAME] this.gameId: HNIEAW
 [ONLINE GAME] window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME] this.socket.id: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME] Pesca carta - invio gameAction al server...
 [ONLINE GAME] gameAction inviato per drawCard
 [DRAW CARD] Pulsante disabilitato, non è possibile pescare carte
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] state.currentPlayerId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(3)
 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 3
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: HNIEAW
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":"white","vertex-a6":"black","vertex-f6":"black"}
 [ADVANTAGE] Componenti: Vertici (1-3), Carte (0-0), Adiacenze (0-1)
 [ADVANTAGE] Valore vantaggio calcolato: -3
 [ADVANTAGE] Bianco ha possibilità di ribaltone (3-1)
 [ADVANTAGE] Bonus ribaltone applicato per il bianco
 [ADVANTAGE] Percentuale finale: 45.0%
 [DEBUG] Vantaggio calcolato: 44.99%
 [ADVANTAGE UI] Bianco ha possibilità di ribaltone (3-1)
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [OPPONENT DRAW DETECTED] L'avversario ha pescato 1 carte
 [OPPONENT ANIMATION BLOCKED] Animazione carte avversario disabilitata per modalità multiplayer
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player1-hand: previousSize=1, currentSize=2, hasPlayedCard=false
 [NAMES PROTECTION] renderHand intercettato, cards: 3
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=3, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Rimuovendo marker esistente
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color white
 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: false
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] Non è il mio turno, fermo timer
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1747
 [TIMER DEBUG] - player2TotalTime: 1757
 [TIMER DEBUG] - gameState.currentPlayerId: player2
 [TIMER] Modalità online - currentPlayerId: player2
 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
 [TIMER] Avviato timer totale per giocatore 2
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: visible  
 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
 [VISIBILITY] Istanza cache cardDealingAmbient fermata
 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [PSN] Mossa registrata (con debug): Turno 7, Nero - Scissors 6 su d5 (controllo vertice ottenuto). Prossimo giocatore: white. StateTurn: 7
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] state.currentPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(2)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 2
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: HNIEAW
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":"white","vertex-a6":"black","vertex-f6":"black"}
 [ADVANTAGE] Componenti: Vertici (1-3), Carte (0-0), Adiacenze (0-1)
 [ADVANTAGE] Valore vantaggio calcolato: -3
 [ADVANTAGE] Bianco ha possibilità di ribaltone (3-1)
 [ADVANTAGE] Bonus ribaltone applicato per il bianco
 [ADVANTAGE] Percentuale finale: 45.0%
 [DEBUG] Vantaggio calcolato: 44.99%
 [ADVANTAGE UI] Bianco ha possibilità di ribaltone (3-1)
 [HISTORY] Aggiunta mossa #11 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player2-hand: previousSize=3, currentSize=2, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player2-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Scissors-6
 [REMOVE OPPONENT CARD] Rimuovendo carta Scissors-6 da player2-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player2-hand
 [OPPONENT HAND] Carta dell'avversario Scissors-6 rimossa permanentemente da player2-hand - NON verrà ricreata
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
 [OPPONENT MARKER] Identificata ultima mossa avversario in d5
 [OPPONENT MARKER] Aggiungendo marker all'ultima carta avversario in d5
 [DEBUG] updateGameUI AFTER renderBoard
 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color white
 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color black
 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
 [TIMER DEBUG ENTRY] gameState.mode: online
 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
 [TIMER DEBUG] - isGameReady: true
 [TIMER DEBUG] - isMyTurn: true
 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
 [TIMER DEBUG] Game è pronto, controllo turno...
 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
 [TIMER] Avvio timer di turno: 60 secondi rimanenti
 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
 [TIMER DEBUG] - window: true
 [TIMER DEBUG] - window.currentGameState: true
 [TIMER DEBUG] - player1TotalTime: 1747
 [TIMER DEBUG] - player2TotalTime: 1754
 [TIMER DEBUG] - gameState.currentPlayerId: player1
 [TIMER] Modalità online - currentPlayerId: player1
 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
 [TIMER] Avviato timer totale per giocatore 1
 [PSN] Usando handSize dal server per white: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
 [VISIBILITY] Eseguo pulizia cover cards bloccate...
 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: hidden  
 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [DRAG START] Inizio trascinamento carta Scissors-K del giocatore white
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: white
 [DEBUG] effectiveTurnPlayerId: player1
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Scissors Card value: K Type of value: string
 Current player hand: Array(2)
 Full hand data: [{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Scissors-K","suit":"Scissors","value":"K"}]
 Card data being sent: Object
 [DRAG END] DropEffect: none
 [DRAG START] Inizio trascinamento carta Paper-J del giocatore white
 [DRAG START] isGameRunning: true, gameOver: false, isSetupAnimating: false
 [DRAG START] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [DRAG ONLINE] È il mio turno? true
 [DRAG ONLINE] Il mio colore: white
 [DEBUG] effectiveTurnPlayerId: player1
 [DEBUG] turnPlayer extracted: Object
 Drag Start: Object
 Card suit: Paper Card value: J Type of value: string
 Current player hand: Array(2)
 Full hand data: [{"id":"Paper-J","suit":"Paper","value":"J"},{"id":"Scissors-K","suit":"Scissors","value":"K"}]
 Card data being sent: Object
 [CELL DROP LISTENER] Evento drop catturato su cella: cell-d1 Event target: cell-d1
 [DROP SUCCESS] Carta rilasciata su cella: cell-d1 Drop ID: 1751023738780_lg9ika4h6 Timestamp: 2025-06-27T11:28:58.780Z
 [DROP PROTECTION] Setting isProcessingAction = true and processingDropId = 1751023738780_lg9ika4h6
 Dropped Card Data: Object
 [DROP CELL] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
 [DROP ONLINE] È il mio turno? true
 [DROP ONLINE] Il mio colore: white
 [DROP DEBUG] effectiveTurnPlayerId: player1
 [DROP DEBUG] turnPlayer extracted: Object
 (white) tenta di piazzare [DRAG] J di Paper su d1
 [ACTION] Calling gameModeManager.placeCard, Drop ID: 1751023738780_lg9ika4h6
 [GAME MODE] Tentativo di piazzare carta: Object in posizione: d1
 [ONLINE GAME] Posizionamento carta: Object in d1
 [DROP] Carta temporanea aggiunta alla cella: d1
 [OPPONENT MARKER] Rimuovendo marker esistente
 [OPPONENT MARKER] Rimossi tutti i marker - il giocatore locale ha fatto una mossa
 [DRAG END] DropEffect: move
 [PSN] Usando handSize dal server per white: 2 carte rimanenti
 [PSN CAPTURE] Modalità multiplayer - NON registro mossa (gestita dal server)
 [SOCKET] Ricevuto evento gameState: Object
 [SOCKET] Chiamando handleGameStateEvent
 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
 [HANDLE GAME STATE] Debug GameModeManager:
 [HANDLE GAME STATE] - gameModeManager exists: true
 [HANDLE GAME STATE] - currentManager: OnlineGameManager
 [HANDLE GAME STATE] - currentMode: online
 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
 [GAME STATE] Stato ricevuto mentre processingDropId = 1751023738780_lg9ika4h6
 [GAME STATE] Turno cambiato, reset dei flag di elaborazione
 [PSN] Mossa registrata (con debug): Turno 8, Bianco - Paper J su d1 (controllo vertice ottenuto). Prossimo giocatore: black. StateTurn: 8
 [PSN] Skip rilevazione pescate - registrazione dal server in corso
 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
 [TURN PROTECTION] Turno era protetto: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] state.currentPlayerId: WnfUL9LYIQLeCal_AAA2
 [ONLINE GAME MAPPING] localPlayerData: Object
 [ONLINE GAME MAPPING] opponentPlayerData: Object
 [GAME STATE] Stato mani dopo aggiornamento:
 [GAME STATE] player1 (white): Array(1)
 [GAME STATE] player2 (black): Array(2)
 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
 [PSN API] Aggiornamento handSize dai dati del server
 [PSN API] Aggiornato handSize bianco: 1
 [PSN API] Aggiornato handSize nero: 2
 [ANIMATION DEBUG] Analisi condizioni per animazioni:
 [ANIMATION DEBUG] - isStarting: false
 [ANIMATION DEBUG] - wasGameRunning: true
 [ANIMATION DEBUG] - state.gameId: HNIEAW
 [ANIMATION DEBUG] - state.gameOver: false
 [ANIMATION DEBUG] - isSetupAnimating: false
 [ANIMATION DEBUG] - isFirstStateReceived: false
 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
 [ANIMATION DEBUG] - state.mode: online
 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
 [ANIMATION DEBUG] - needsAnimations: false
 [UPDATE UI] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":"white","vertex-a6":"black","vertex-f6":"black"}
 [ADVANTAGE] Componenti: Vertici (1-3), Carte (1-0), Adiacenze (0-1)
 [ADVANTAGE] Valore vantaggio calcolato: -2
 [ADVANTAGE] Bianco ha possibilità di ribaltone (3-1)
 [ADVANTAGE] Bonus ribaltone applicato per il bianco
 [ADVANTAGE] Percentuale finale: 51.7%
 [DEBUG] Vantaggio calcolato: 51.66%
 [ADVANTAGE UI] Bianco ha possibilità di ribaltone (3-1)
 [HISTORY] Aggiunta mossa #12 alla cronologia
 Tentativo di forzare rendering rating...
 Ratings dopo init: Object
 Aggiornamento avatar player1: player1
 Aggiornamento avatar player2: player2
 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: WnfUL9LYIQLeCal_AAA2
 [PERSISTENCE] Salvataggio stato partita...
 [PERSISTENCE] Stato salvato: Object
 [ONLINE UI] Player1/Player2 già mappati da game mode manager
 [NAMES PROTECTION] renderHand intercettato, cards: 1
 [OPPONENT HAND] player1-hand: previousSize=2, currentSize=1, hasPlayedCard=true
 [OPPONENT HAND] Avversario ha giocato una carta in player1-hand, lascio slot vuoto
 [OPPONENT HAND] Carta giocata dall'avversario: Paper-J
 [REMOVE OPPONENT CARD] Rimuovendo carta Paper-J da player1-hand
 [REMOVE OPPONENT CARD] Carta rimossa, slot lasciato vuoto in player1-hand
 [OPPONENT HAND] Carta dell'avversario Paper-J rimossa permanentemente da player1-hand - NON verrà ricreata
 [NAMES PROTECTION] renderHand intercettato, cards: 2
 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=2, hasPlayedCard=false
 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5321 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:14917 [DEBUG] updateGameUI AFTER renderBoard
script.js:14918 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
script.js:14919 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color white
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color black
script.js:14939 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14940 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:16253 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: WnfUL9LYIQLeCal_AAA2
script.js:13107 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1533 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1535 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player2
multiplayer.js:1536 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1565 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1566 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1567 [TIMER DEBUG] - isMyTurn: false
multiplayer.js:1568 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1571 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1577 [TIMER DEBUG] Non è il mio turno, fermo timer
multiplayer.js:1588 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1589 [TIMER DEBUG] - window: true
multiplayer.js:1590 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1591 [TIMER DEBUG] - player1TotalTime: 1740
multiplayer.js:1592 [TIMER DEBUG] - player2TotalTime: 1754
multiplayer.js:1593 [TIMER DEBUG] - gameState.currentPlayerId: player2
multiplayer.js:1605 [TIMER] Modalità online - currentPlayerId: player2
multiplayer.js:1606 [TIMER] isPlayer1Turn (bianco): false isPlayer2Turn (nero): true
multiplayer.js:1677 [TIMER] Avviato timer totale per giocatore 2
psn-unified.js:1156 [PSN CAPTURE] ⚠️ Skip cattura - già in corso registrazione autoritativa dal server
script.js:1549 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: visible  
script.js:2203 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2224 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2242 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2572 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:10660 [DROP PROTECTION] Auto-reset isProcessingAction = false and processingDropId = null after timeout
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
script.js:11171 [ID SYNC] ID corretto confermato: BYf4pH-dYHLQg5E8AAA4 per username: giggio
script.js:11184 [HANDLE GAME STATE] Debug GameModeManager:
script.js:11185 [HANDLE GAME STATE] - gameModeManager exists: true
script.js:11186 [HANDLE GAME STATE] - currentManager: OnlineGameManager
script.js:11187 [HANDLE GAME STATE] - currentMode: online
script.js:11188 [HANDLE GAME STATE] - window.myPlayerId: BYf4pH-dYHLQg5E8AAA4
script.js:11246 [HANDLE GAME STATE] GameModeManager già inizializzato per partite online
script.js:11960 [TURN PROTECTION] 🔓 Setup e post-setup completati - rimuovo protezione del turno
script.js:11961 [TURN PROTECTION] Turno era protetto: BYf4pH-dYHLQg5E8AAA4
online-game.js:148 [ONLINE GAME MAPPING] === DEBUG MAPPING ===
online-game.js:149 [ONLINE GAME MAPPING] this.myPlayerId: BYf4pH-dYHLQg5E8AAA4
online-game.js:150 [ONLINE GAME MAPPING] this.opponentId: WnfUL9LYIQLeCal_AAA2
online-game.js:151 [ONLINE GAME MAPPING] state.currentPlayerId: BYf4pH-dYHLQg5E8AAA4
online-game.js:152 [ONLINE GAME MAPPING] localPlayerData: Object
online-game.js:153 [ONLINE GAME MAPPING] opponentPlayerData: Object
script.js:12158 [GAME STATE] Stato mani dopo aggiornamento:
script.js:12161 [GAME STATE] player1 (white): Array(1)
script.js:12161 [GAME STATE] player2 (black): Array(3)
script.js:12381 [PERMANENT NAMES] Nomi permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"bruscolino","BYf4pH-dYHLQg5E8AAA4":"giggio"}
script.js:12403 [PERMANENT COLORS] Colori permanenti finali: {"WnfUL9LYIQLeCal_AAA2":"black","BYf4pH-dYHLQg5E8AAA4":"white"}
psn-unified.js:109 [PSN API] Aggiornamento handSize dai dati del server
psn-unified.js:119 [PSN API] Aggiornato handSize bianco: 1
psn-unified.js:122 [PSN API] Aggiornato handSize nero: 3
script.js:12475 [ANIMATION DEBUG] Analisi condizioni per animazioni:
script.js:12476 [ANIMATION DEBUG] - isStarting: false
script.js:12477 [ANIMATION DEBUG] - wasGameRunning: true
script.js:12478 [ANIMATION DEBUG] - state.gameId: HNIEAW
script.js:12479 [ANIMATION DEBUG] - state.gameOver: false
script.js:12480 [ANIMATION DEBUG] - isSetupAnimating: false
script.js:12481 [ANIMATION DEBUG] - isFirstStateReceived: false
script.js:12482 [ANIMATION DEBUG] - window.hasReceivedInitialState: true
script.js:12483 [ANIMATION DEBUG] - state.mode: online
script.js:12484 [ANIMATION DEBUG] - window.animationsCompletedForGame: HNIEAW
script.js:12485 [ANIMATION DEBUG] - needsAnimations: false
script.js:14422 [UPDATE UI] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
script.js:13618 [ADVANTAGE] Calcolo vantaggio con stato: {"vertex-a1":"black","vertex-f1":"white","vertex-a6":"black","vertex-f6":"black"}
script.js:13684 [ADVANTAGE] Componenti: Vertici (1-3), Carte (1-0), Adiacenze (0-1)
script.js:13685 [ADVANTAGE] Valore vantaggio calcolato: -2
script.js:13699 [ADVANTAGE] Bianco ha possibilità di ribaltone (3-1)
script.js:13719 [ADVANTAGE] Bonus ribaltone applicato per il bianco
script.js:13741 [ADVANTAGE] Percentuale finale: 51.7%
script.js:14450 [DEBUG] Vantaggio calcolato: 51.66%
script.js:13969 [ADVANTAGE UI] Bianco ha possibilità di ribaltone (3-1)
script.js:15932 Tentativo di forzare rendering rating...
script.js:15959 Ratings dopo init: Object
script.js:15968 Aggiornamento avatar player1: player1
script.js:15974 Aggiornamento avatar player2: player2
script.js:16253 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
script.js:14522 [ONLINE UI] Nomi basati sui colori: P1 (BIANCO)=giggio, P2 (NERO)=bruscolino
script.js:14525 [UI] Nomi predefiniti: P1=giggio, P2=bruscolino
script.js:14540 [UPDATE UI FINAL] originalCurrentPlayerId già presente nello stato: BYf4pH-dYHLQg5E8AAA4
script.js:37 [PERSISTENCE] Salvataggio stato partita...
script.js:67 [PERSISTENCE] Stato salvato: Object
script.js:14568 [OPPONENT DRAW DETECTED] L'avversario ha pescato 1 carte
script.js:14572 [OPPONENT ANIMATION BLOCKED] Animazione carte avversario disabilitata per modalità multiplayer
script.js:14659 [ONLINE UI] Player1/Player2 già mappati da game mode manager
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 1
script.js:4992 [CARD DRAG] Rendendo trascinabile ULTIMA carta Scissors-K in player1-hand
script.js:4997 [CARD DRAG] Listener aggiunti per ULTIMA carta. Draggable: true, Cursor: grab
player-names-protection.js:285 [NAMES PROTECTION] renderHand intercettato, cards: 3
script.js:4804 [OPPONENT HAND] player2-hand: previousSize=2, currentSize=3, hasPlayedCard=false
script.js:14914 [DEBUG] updateGameUI BEFORE renderBoard - boardArea.offsetHeight: 1261
script.js:14915 [DEBUG] updateGameUI BEFORE renderBoard - gameBoardElement.offsetHeight: 1224
script.js:5308 [DEBUG] renderBoard START - gameBoardElement.innerHTML (inizio): <div class="cell" id="cell-a1" data-row="1" data-col="a" data-notation="a1" data-listeners-added="tr...
script.js:5321 [DEBUG] renderBoard - Animazioni completate e tabellone esistente, uso updateBoardCardsOnly
script.js:14917 [DEBUG] updateGameUI AFTER renderBoard
script.js:14918 [DEBUG] updateGameUI AFTER renderBoard - gameBoardElement.offsetHeight: 1224
script.js:14919 [DEBUG] updateGameUI AFTER renderBoard - boardArea.offsetHeight: 1261
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-a1 with color black
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-f1 with color white
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-a6 with color black
script.js:5565 [DEBUG VERTEX TOKEN] Creating token for vertex-f6 with color black
script.js:14939 [RIBALTONE UI] Controllo messaggio ribaltone. state.ribaltoneMessage: non presente
script.js:14940 [RIBALTONE UI] Controllo messaggio ribaltone. state.continueForOneTurn: false
script.js:16253 [GIOCA TAB] Protezione turno non attiva - Uso ID dal server: BYf4pH-dYHLQg5E8AAA4
script.js:13107 [TURN TIMER] Chiamando updateGameStateWithTurnTimer per partita online
multiplayer.js:1533 [TIMER DEBUG ENTRY] updateGameStateWithTurnTimer chiamata con gameState: Object
multiplayer.js:1534 [TIMER DEBUG ENTRY] gameState.turnTimeRemaining: 60
multiplayer.js:1535 [TIMER DEBUG ENTRY] gameState.currentPlayerId: player1
multiplayer.js:1536 [TIMER DEBUG ENTRY] gameState.mode: online
multiplayer.js:1565 [TIMER DEBUG] Controllo condizioni di base per avvio timer:
multiplayer.js:1566 [TIMER DEBUG] - isGameReady: true
multiplayer.js:1567 [TIMER DEBUG] - isMyTurn: true
multiplayer.js:1568 [TIMER DEBUG] - gameState.turnTimeRemaining: 60
multiplayer.js:1571 [TIMER DEBUG] Game è pronto, controllo turno...
multiplayer.js:1573 [TIMER DEBUG] È il mio turno, avvio timer con tempo: 60
multiplayer.js:1006 [TIMER] Avvio timer di turno: 60 secondi rimanenti
multiplayer.js:1588 [TIMER DEBUG] Controllo condizioni per avvio timer totali:
multiplayer.js:1589 [TIMER DEBUG] - window: true
multiplayer.js:1590 [TIMER DEBUG] - window.currentGameState: true
multiplayer.js:1591 [TIMER DEBUG] - player1TotalTime: 1740
multiplayer.js:1592 [TIMER DEBUG] - player2TotalTime: 1751
multiplayer.js:1593 [TIMER DEBUG] - gameState.currentPlayerId: player1
multiplayer.js:1605 [TIMER] Modalità online - currentPlayerId: player1
multiplayer.js:1606 [TIMER] isPlayer1Turn (bianco): true isPlayer2Turn (nero): false
multiplayer.js:1619 [TIMER] Avviato timer totale per giocatore 1
script.js:2370 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2373 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
script.js:1549 [SOCKET] Cambio di visibilità del client WnfUL9LYIQLeCal_AAA2: hidden  
script.js:1555 [SOCKET] Un altro client è minimizzato, pronto a gestire le sue animazioni se necessario
psn-unified.js:2908 [PSN] Controllo periodico: Ripristino notazione esistente
psn-unified.js:882 [PSN] Mosse presenti dopo ritorno visibilità - forza sincronizzazione
psn-unified.js:915 [PSN] Sistema PSN standard non disponibile - cattura stato corrente
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
